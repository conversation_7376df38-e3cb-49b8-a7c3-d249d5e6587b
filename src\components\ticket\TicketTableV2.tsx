import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "../ui/table";

import Badge from "../ui/badge/Badge";
import Image from "next/image";
import { Agent, TicketPagination } from "@/types";
import { ticketApi } from "@/api/ticketApi";
import clsx from "clsx";
import Link from "next/link";
import { AppRouting } from "@/constants/routes";
import Pagination from "../ui/pagination/Pagination";
import SearchBar from "../ui/search/SearchBar";
import { FiEye, FiEdit3, FiClock, FiUser, FiTool, FiCalendar, FiCheckCircle, FiXCircle, FiRefreshCw, FiSlash } from "react-icons/fi";
import { PiListNumbers } from "react-icons/pi";



interface TicketTableProps {
  agent: Agent | null;
}

const statusColors: Record<string, string> = {
  'Chờ xử lý': 'bg-amber-50 text-amber-700 border-amber-200',
  '<PERSON><PERSON>n thành': 'bg-emerald-50 text-emerald-700 border-emerald-200',
  'Đã từ chối': 'bg-red-50 text-red-700 border-red-200',
  'Đang xử lý': 'bg-blue-50 text-blue-700 border-blue-200',
  'Đã hủy': 'bg-gray-50 text-gray-700 border-gray-200'
};


const statusIcons: Record<string, React.ReactNode> = {
  'Chờ xử lý': <FiClock className="w-3 h-3 text-yellow-500" />,
  'Hoàn thành': <FiCheckCircle className="w-3 h-3 text-emerald-500" />,
  'Đã từ chối': <FiXCircle className="w-3 h-3 text-red-500" />,
  'Đang xử lý': <FiRefreshCw className="w-3 h-3 text-blue-500 animate-spin-slow" />,
  'Đã hủy': <FiSlash className="w-3 h-3 text-gray-500" />
}



// Define the table data using the interface

export default function TicketTableV2({agent} : TicketTableProps) {
    const [tickets, setTickets] = useState<TicketPagination | null>(null);
    const [page, setPage] = useState<number>(1);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>('Không thể lấy danh sách ticket.');
    const [filterStatus, setFilterStatus] = useState<string>('')
    const [search, setSearch] = useState<string>('')
    const [hoveredRow, setHoveredRow] = useState<string | null>(null);

    const fetchTickets = async () => {
        setLoading(true);
        setError(null);
        try {
            const res = await ticketApi.getTicketPagination(agent ? agent.moduleId: undefined, search, filterStatus, 7, (page - 1)* 7);
            setTickets(res);
        } catch (err) {
            setError('Không thể lấy danh sách ticket.');
        } finally {
            setLoading(false);
        }
    };
    const onChangePage = async (e: any) => {
        let value = e.target.value;
        if(value < 1) 
            return;
        else
            setPage(value)
    }

    useEffect(() => {
    fetchTickets();
    }, [agent, filterStatus, page]);

    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
        fetchTickets();
        }
    }
    const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterStatus(e.target.value)  
    }

    if (error || !tickets) return (
        <div className="p-8 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-50 dark:bg-none mb-4">
                <div className="w-6 h-6 rounded-full bg-red-500"></div>
            </div>
            <p className="text-red-600 font-medium">{error}</p>
        </div>
    );

    return (
        <div className="overflow-hidden rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-none shadow-sm hover:shadow-md transition-shadow duration-300 dark:bg-white/[0.03]">
        <div className="max-w-full overflow-x-auto">
            {/* Enhanced Header */}
            <div className="flex flex-wrap justify-between gap-4 items-center p-6 bg-gray-50/50 dark:bg-gray-800/50 border-b border-gray-100 dark:border-gray-600">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-100">
                        <FiUser className="w-4 h-4 dark:text-gray-100" />
                        <span className="font-medium">{tickets.totalCount}</span>
                        <span>yêu cầu</span>
                    </div>
                    <div className="w-1 h-4 bg-gray-300 rounded-full "></div>
                    <div className="text-sm text-gray-500 dark:text-gray-100">
                        Trang <input type="number" value={page} className="w-8 h-8 outline-none text-center font-medium text-gray-600 dark:text-gray-100 border-b-2 mx-2" min={1} onChange={(e) => onChangePage(e)}></input> / {Math.ceil(tickets.totalCount / tickets.pageSize)}
                    </div>
                </div>
                <div className="flex gap-3">
                    <select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        className="px-4 py-2.5 border border-gray-200 dark:border-gray-600 rounded-lg bg-white focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                    >
                        <option value="">Tất cả trạng thái</option>
                        <option value="Chờ xử lý">Chờ xử lý</option>
                        <option value="Bị từ chối">Bị từ chối</option>
                        <option value="Đã đóng">Đã đóng</option>
                    </select>

                    <SearchBar
                        value={search}
                        onChange={setSearch}
                        onKeyDown={handleSearchKeyDown}
                        placeholder="Tìm kiếm yêu cầu..."
                    />
                </div>
            </div>
            <div className="min-w-[1102px]">
            <div className="table-container" style={{ minHeight: '620px' }}>
            <Table>
                {/* Enhanced Table Header */}
                <TableHeader className="bg-gray-50/80 dark:bg-gray-700 border-b border-gray-200  dark:border-gray-600">
                    <TableRow>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <PiListNumbers  className="w-4 h-4 text-gray-400" />
                            STT
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <FiEdit3 className="w-4 h-4 text-gray-400" />
                            Mã yêu cầu
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <FiUser className="w-4 h-4 text-gray-400" />
                            Người yêu cầu
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <FiTool className="w-4 h-4 text-gray-400" />
                            Tool tiếp nhận
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <FiClock className="w-4 h-4 text-gray-400" />
                            Trạng thái
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-start text-sm dark:text-gray-300 tracking-wide"
                        >
                        <div className="flex items-center gap-2">
                            <FiCalendar className="w-4 h-4 text-gray-400" />
                            Ngày tạo
                        </div>
                        </TableCell>
                        <TableCell
                        isHeader
                        className="px-6 py-4 font-semibold text-gray-700 text-center text-sm dark:text-gray-300 tracking-wide"
                        >
                        Thao tác
                        </TableCell>
                    </TableRow>
                </TableHeader>

                {/* Enhanced Table Body with Fixed Height */}
                <TableBody className="divide-y divide-gray-50 dark:divide-white/[0.05]">
                {(
                    <>
                    {/* Render actual data */}
                    {tickets?.data && tickets.data.map((ticket, index) => (
                        <TableRow
                            key={ticket.id}
                            className={clsx(
                                "group hover:bg-blue-50/50 transition-all duration-200 cursor-pointer",
                                "border-b border-gray-100 dark:border-white/[0.05]",
                                hoveredRow === String(ticket.id) && "bg-blue-50/30"
                            )}
                        >
                            <TableCell className="px-6 py-5 text-gray-600 text-start text-sm dark:text-gray-400">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-8 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                                    <span className="font-medium">{(page - 1)* 7 + index + 1}</span>
                                </div>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-start">
                                <div className="flex items-center gap-3">
                                    
                                    <div>
                                        <Link
                                            className="block font-semibold text-gray-900 text-sm hover:text-blue-600 transition-colors duration-200 dark:text-white/90"
                                            href={`${AppRouting.ticketDetailPage}/${ticket.id}`}
                                        >
                                            {ticket.title}
                                        </Link>
                                        <div className="text-xs text-gray-500 mt-1">ID: {ticket.id}</div>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-gray-600 text-start text-sm dark:text-gray-400">
                                <div className="flex items-center gap-2">
                                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <FiUser className="w-4 h-4 text-gray-500" />
                                    </div>
                                    <span className="font-medium">{ticket.createdBy || 'Không rõ'}</span>
                                </div>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-gray-600 text-start text-sm dark:text-gray-400">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="font-medium">{ticket.moduleName || 'N/A'}</span>
                                </div>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-start">
                                <span
                                    className={clsx(
                                        'inline-flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-semibold border',
                                        statusColors[ticket.overallStatus ? ticket.overallStatus : "Chờ xử lý"]
                                    )}
                                >
                                    {statusIcons[ticket.overallStatus ? ticket.overallStatus : "Chờ xử lý"]}
                                    {ticket.overallStatus ? ticket.overallStatus : "Chờ xử lý"}
                                </span>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-gray-600 text-sm dark:text-gray-400">
                                <div className="flex flex-col">
                                    <span className="font-medium">
                                        {new Date(ticket.createdAt).toLocaleDateString('vi-VN')}
                                    </span>
                                    <span className="text-xs text-gray-400">
                                        {new Date(ticket.createdAt).toLocaleTimeString('vi-VN', {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </span>
                                </div>
                            </TableCell>
                            <TableCell className="px-6 py-5 text-center">
                                <div className="flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <Link
                                        href={`${AppRouting.ticketDetailPage}/${ticket.id}`}
                                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                                        title="Xem chi tiết"
                                    >
                                        <FiEye className="w-4 h-4" />
                                    </Link>
                                </div>
                            </TableCell>
                        </TableRow>
                    ))}

                    {/* Render empty rows to maintain consistent height */}
                    {tickets?.data && Array.from({ length: Math.max(0, 7 - tickets.data.length) }).map((_, index) => (
                        <TableRow key={`empty-${index}`} className="border-b border-gray-100 dark:border-white/[0.05]">
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                            <TableCell className="px-6 py-5">&nbsp;</TableCell>
                        </TableRow>
                    ))}
                    </>
                )}
                </TableBody>
            </Table>
            </div>
            </div>

            {/* Enhanced Footer */}
            <div className="px-6 py-4 bg-gray-50/30 dark:bg-gray-800/90 border-t border-gray-100 dark:border-gray-600 flex items-center justify-between ">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="dark:text-gray-100">
                        Hiển thị <span className="font-semibold text-gray-900 dark:text-gray-400">{((page - 1) * tickets.pageSize) + 1}</span> - <span className="font-semibold text-gray-900 dark:text-gray-400">{Math.min(page * tickets.pageSize, tickets.totalCount)}</span> trong tổng số <span className="font-semibold text-gray-900 dark:text-gray-400">{tickets.totalCount}</span> yêu cầu
                    </span>
                </div>
                <Pagination
                    currentPage={page}
                    totalPages={Math.ceil(tickets.totalCount / tickets.pageSize)}
                    onPageChange={setPage}
                    totalItems={tickets.totalCount}
                    itemsPerPage={tickets.pageSize}
                />
            </div>
        </div>
    </div>
  );
}

// Add CSS animations
const styles = `
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

/* Mobile Conversation Overlay Animations */
.overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-in-out;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.overlay.closing {
  animation: fadeOut 0.3s ease-in-out;
}

.sidebar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 320px;
  max-width: 85vw;
  background-color: white;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 0 16px 16px 0;
  overflow: hidden;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar.closing {
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.6, 0, 0.8, 1);
}

/* Mobile header styles */
.mobileHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.menuButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  transition: color 0.2s ease-in-out;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.menuButton:hover {
  color: #1f2937;
  background-color: #f3f4f6;
}

.newChatButton {
  padding: 0.5rem;
  color: #2563eb;
  transition: color 0.2s ease-in-out;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 0.5rem;
}

.newChatButton:hover {
  color: #1d4ed8;
  background-color: #eff6ff;
}

.conversationTitle {
  font-weight: 500;
  font-size: 0.875rem;
  color: #374151;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Close button in sidebar */
.closeButton {
  padding: 0.5rem;
  color: #6b7280;
  transition: color 0.2s ease-in-out;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 0.5rem;
}

.closeButton:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .sidebar {
    width: 280px;
  }

  .conversationTitle {
    max-width: 150px;
  }

  .mobileHeader {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
    max-width: 100vw;
    border-radius: 0;
  }

  .conversationTitle {
    max-width: 120px;
  }

  .mobileHeader {
    padding: 0.75rem;
  }
}

@media (max-width: 360px) {
  .conversationTitle {
    max-width: 100px;
    font-size: 0.8rem;
  }

  .mobileHeader {
    padding: 0.5rem;
  }

  .menuButton {
    gap: 0.25rem;
  }
}
